@echo off
echo تشغيل نظام المحاسبة في وضع التصحيح
echo ===================================
echo.

echo سيتم تشغيل النظام في وضع التصحيح مع عرض رسائل الخطأ.
echo استخدم هذا الوضع فقط عند مواجهة مشاكل في التشغيل العادي.
echo.

set /p choice=هل تريد المتابعة؟ (y/n): 

if not "%choice%"=="y" (
    echo تم إلغاء العملية.
    pause
    exit /b
)

echo جاري تشغيل النظام في وضع التصحيح...
echo.
echo سيتم عرض رسائل الخطأ في هذه النافذة.
echo لا تغلق هذه النافذة أثناء تشغيل النظام.
echo.

start /wait "" "نظام_المحاسبة_محمد_الأشرفي.exe"

echo.
echo تم إغلاق النظام.
echo إذا واجهت أي مشاكل، يرجى التواصل مع الدعم الفني.
echo.

pause@echo off
echo تشغيل نظام المحاسبة في وضع التصحيح
echo ===================================
echo.

echo سيتم تشغيل النظام في وضع التصحيح مع عرض رسائل الخطأ.
echo استخدم هذا الوضع فقط عند مواجهة مشاكل في التشغيل العادي.
echo.

set /p choice=هل تريد المتابعة؟ (y/n): 

if not "%choice%"=="y" (
    echo تم إلغاء العملية.
    pause
    exit /b
)

echo جاري تشغيل النظام في وضع التصحيح...
echo.
echo سيتم عرض رسائل الخطأ في هذه النافذة.
echo لا تغلق هذه النافذة أثناء تشغيل النظام.
echo.

start /wait "" "نظام_المحاسبة_محمد_الأشرفي.exe"

echo.
echo تم إغلاق النظام.
echo إذا واجهت أي مشاكل، يرجى التواصل مع الدعم الفني.
echo.

pause