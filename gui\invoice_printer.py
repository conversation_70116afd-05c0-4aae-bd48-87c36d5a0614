from reportlab.lib import colors
from reportlab.lib.pagesizes import A4
from reportlab.lib.units import cm
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import arabic_reshaper
from bidi.algorithm import get_display
from datetime import datetime
import os

class InvoicePrinter:
    def __init__(self, engine):
        self.engine = engine
        # تسجيل الخط العربي
        font_path = os.path.join(os.path.dirname(__file__), '..', 'fonts', 'arial.ttf')
        pdfmetrics.registerFont(TTFont('Arabic', font_path))

    def create_invoice(self, transaction_id, output_path):
        doc = SimpleDocTemplate(
            output_path,
            pagesize=A4,
            rightMargin=1.5*cm,
            leftMargin=1.5*cm,
            topMargin=1.5*cm,
            bottomMargin=1.5*cm
        )

        # جمع بيانات الفاتورة
        with Session(self.engine) as session:
            transaction = session.query(Transaction).get(transaction_id)
            if not transaction:
                raise ValueError("الفاتورة غير موجودة")

            story = []
            
            # ترويسة الفاتورة
            header_data = [
                [self.arabic_text("فاتورة مبيعات"), "", "", self.arabic_text(f"رقم: {transaction.id:06d}")],
                [self.arabic_text("التاريخ:"), self.arabic_text(transaction.date.strftime("%Y-%m-%d")), 
                 self.arabic_text("الوقت:"), self.arabic_text(transaction.date.strftime("%H:%M"))],
                [self.arabic_text("العميل:"), self.arabic_text(transaction.customer.name if transaction.customer else ""),
                 self.arabic_text("الهاتف:"), self.arabic_text(transaction.customer.phone if transaction.customer else "")]
            ]
            
            header_table = Table(header_data, colWidths=[4*cm, 5*cm, 3*cm, 5*cm])
            header_table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (-1, -1), 'Arabic'),
                ('FONTSIZE', (0, 0), (-1, 0), 16),
                ('FONTSIZE', (0, 1), (-1, -1), 12),
                ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
                ('GRID', (0, 0), (-1, -1), 1, colors.grey),
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
                ('SPAN', (0, 0), (2, 0)),  # دمج خلايا العنوان
            ]))
            story.append(header_table)
            story.append(Spacer(1, 20))

            # تفاصيل المنتجات
            items_data = [[
                self.arabic_text("المنتج"),
                self.arabic_text("الكمية"),
                self.arabic_text("السعر"),
                self.arabic_text("الإجمالي")
            ]]
            
            for item in transaction.items:
                product = session.query(Product).get(item.product_id)
                items_data.append([
                    self.arabic_text(product.name),
                    str(item.quantity),
                    f"{item.price:,.2f}",
                    f"{(item.quantity * item.price):,.2f}"
                ])

            # إجماليات الفاتورة
            subtotal = sum(item.quantity * item.price for item in transaction.items)
            tax = subtotal * 0.15  # 15% ضريبة القيمة المضافة
            total = transaction.total_amount

            items_data.extend([
                ["", "", self.arabic_text("المجموع الفرعي:"), f"{subtotal:,.2f}"],
                ["", "", self.arabic_text("الضريبة (15%):"), f"{tax:,.2f}"],
                ["", "", self.arabic_text("الإجمالي:"), f"{total:,.2f}"],
                ["", "", self.arabic_text("المدفوع:"), f"{transaction.paid_amount:,.2f}"],
                ["", "", self.arabic_text("المتبقي:"), f"{(total - transaction.paid_amount):,.2f}"]
            ])

            items_table = Table(items_data, colWidths=[8*cm, 3*cm, 3*cm, 3*cm])
            items_table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (-1, -1), 'Arabic'),
                ('FONTSIZE', (0, 0), (-1, -1), 12),
                ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
                ('ALIGN', (1, 1), (1, -1), 'CENTER'),  # توسيط الكميات
                ('GRID', (0, 0), (-1, -1), 1, colors.grey),
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
                ('BACKGROUND', (2, -5), (-1, -1), colors.lightgrey),  # تظليل صف الإجماليات
            ]))
            story.append(items_table)

            # الشروط والأحكام
            story.append(Spacer(1, 20))
            terms = [
                self.arabic_text("الشروط والأحكام:"),
                self.arabic_text("1. البضاعة المباعة لا ترد ولا تستبدل بعد خروجها من المحل"),
                self.arabic_text("2. يرجى الاحتفاظ بالفاتورة للمراجعة"),
                self.arabic_text("3. مدة الضمان حسب الشروط المحددة لكل منتج")
            ]
            
            for term in terms:
                story.append(Paragraph(term))

            doc.build(story)

    def arabic_text(self, text):
        if not text:
            return ""
        reshaped_text = arabic_reshaper.reshape(str(text))
        return get_display(reshaped_text)