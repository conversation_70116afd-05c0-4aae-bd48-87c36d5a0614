@echo off
echo تشغيل نظام المحاسبة المتكامل - محمد الأشرفي
echo ===================================
echo.
echo جاري التحقق من وجود الملفات المطلوبة...

if not exist "نظام_المحاسبة_محمد_الأشرفي.exe" (
    echo خطأ: الملف التنفيذي غير موجود!
    pause
    exit /b
)

if not exist "fonts" (
    echo تحذير: مجلد الخطوط غير موجود!
    mkdir fonts
)

if not exist "images" (
    echo تحذير: مجلد الصور غير موجود!
    mkdir images
)

echo جميع الملفات موجودة. جاري تشغيل النظام...
echo.

start "" "نظام_المحاسبة_محمد_الأشرفي.exe"

exit@echo off
echo تشغيل نظام المحاسبة المتكامل - محمد الأشرفي
echo ===================================
echo.
echo جاري التحقق من وجود الملفات المطلوبة...

if not exist "نظام_المحاسبة_محمد_الأشرفي.exe" (
    echo خطأ: الملف التنفيذي غير موجود!
    pause
    exit /b
)

if not exist "fonts" (
    echo تحذير: مجلد الخطوط غير موجود!
    mkdir fonts
)

if not exist "images" (
    echo تحذير: مجلد الصور غير موجود!
    mkdir images
)

echo جميع الملفات موجودة. جاري تشغيل النظام...
echo.

start "" "نظام_المحاسبة_محمد_الأشرفي.exe"

exit