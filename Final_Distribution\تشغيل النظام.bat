@echo off
chcp 65001 >nul
echo تشغيل نظام المحاسبة المتكامل - محمد الأشرفي
echo ===================================
echo.
echo جاري التحقق من وجود الملفات المطلوبة...

if not exist "نظام_المحاسبة_محمد_الأشرفي.exe" (
    echo خطأ: الملف التنفيذي غير موجود!
    echo يرجى التأكد من وجود الملف في نفس مجلد هذا الملف التنفيذي
    pause
    exit /b 1
)

if not exist "fonts" (
    echo تحذير: مجلد الخطوط غير موجود! جاري إنشاؤه...
    mkdir fonts
    echo تم إنشاء مجلد الخطوط بنجاح
)

if not exist "images" (
    echo تحذير: مجلد الصور غير موجود! جاري إنشاؤه...
    mkdir images
    echo تم إنشاء مجلد الصور بنجاح
)

echo.
echo جميع الملفات موجودة. جاري تشغيل النظام...
echo.

REM تشغيل النظام مع التعامل مع الأخطاء
start "" "نظام_المحاسبة_محمد_الأشرفي.exe"

if %ERRORLEVEL% NEQ 0 (
    echo خطأ في تشغيل النظام!
    echo كود الخطأ: %ERRORLEVEL%
    pause
    exit /b %ERRORLEVEL%
)

echo تم تشغيل النظام بنجاح!
timeout /t 3 /nobreak >nul
exit /b 0