from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                             QPushButton, QLineEdit, QLabel, QSpinBox,
                             QDoubleSpinBox, QTableWidgetItem, QMessageBox,
                             QFrame, QGridLayout, QHeaderView, QComboBox,
                             QTabWidget, QDialog)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QIcon, QColor
from sqlalchemy.orm import Session
from sqlalchemy import or_
from database.models import Product

# قائمة الوحدات المتاحة
UNITS = ["قطعة", "كيلوجرام", "متر", "لتر", "طن", "علبة", "كرتون"]

class ProductDialog(QDialog):
    def __init__(self, engine, product=None, parent=None):
        super().__init__(parent)
        self.engine = engine
        self.product = product
        self.setWindowTitle("إضافة منتج جديد" if not product else "تعديل المنتج")
        self.setup_ui()
        if product:
            self.load_product_data()

    def setup_ui(self):
        layout = QGridLayout()
        self.setLayout(layout)

        # الحقول
        self.code_input = QLineEdit()
        self.code_input.setPlaceholderText("كود المنتج")
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("اسم المنتج")
        self.description_input = QLineEdit()
        self.description_input.setPlaceholderText("وصف المنتج")
        self.category_input = QLineEdit()
        self.category_input.setPlaceholderText("تصنيف المنتج")
        self.unit_combo = QComboBox()
        self.unit_combo.addItems(UNITS)
        self.purchase_price = QDoubleSpinBox()
        self.purchase_price.setMaximum(1000000)
        self.sale_price = QDoubleSpinBox()
        self.sale_price.setMaximum(1000000)
        self.quantity = QSpinBox()
        self.quantity.setMaximum(1000000)
        self.min_quantity = QSpinBox()
        self.min_quantity.setMaximum(1000000)
        self.barcode_input = QLineEdit()
        self.barcode_input.setPlaceholderText("باركود المنتج")

        # إضافة الحقول للنموذج
        layout.addWidget(QLabel("كود المنتج:"), 0, 0)
        layout.addWidget(self.code_input, 0, 1)
        layout.addWidget(QLabel("اسم المنتج:"), 0, 2)
        layout.addWidget(self.name_input, 0, 3)
        
        layout.addWidget(QLabel("الوصف:"), 1, 0)
        layout.addWidget(self.description_input, 1, 1)
        layout.addWidget(QLabel("التصنيف:"), 1, 2)
        layout.addWidget(self.category_input, 1, 3)
        
        layout.addWidget(QLabel("وحدة القياس:"), 2, 0)
        layout.addWidget(self.unit_combo, 2, 1)
        layout.addWidget(QLabel("الباركود:"), 2, 2)
        layout.addWidget(self.barcode_input, 2, 3)
        
        layout.addWidget(QLabel("سعر الشراء:"), 3, 0)
        layout.addWidget(self.purchase_price, 3, 1)
        layout.addWidget(QLabel("سعر البيع:"), 3, 2)
        layout.addWidget(self.sale_price, 3, 3)
        
        layout.addWidget(QLabel("الكمية الحالية:"), 4, 0)
        layout.addWidget(self.quantity, 4, 1)
        layout.addWidget(QLabel("الحد الأدنى:"), 4, 2)
        layout.addWidget(self.min_quantity, 4, 3)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        self.save_btn = QPushButton("حفظ")
        self.save_btn.setStyleSheet("background-color: #28A745; min-width: 100px;")
        self.save_btn.clicked.connect(self.save_product)
        
        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.setStyleSheet("background-color: #6C757D; min-width: 100px;")
        self.cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.save_btn)
        buttons_layout.addWidget(self.cancel_btn)
        layout.addLayout(buttons_layout, 5, 0, 1, 4)

    def load_product_data(self):
        self.code_input.setText(self.product.code or "")
        self.name_input.setText(self.product.name)
        self.description_input.setText(self.product.description or "")
        self.category_input.setText(self.product.category or "")
        if self.product.unit in UNITS:
            self.unit_combo.setCurrentText(self.product.unit)
        self.purchase_price.setValue(self.product.purchase_price)
        self.sale_price.setValue(self.product.sale_price)
        self.quantity.setValue(self.product.quantity)
        self.min_quantity.setValue(self.product.min_quantity)
        self.barcode_input.setText(self.product.barcode or "")

    def save_product(self):
        name = self.name_input.text().strip()
        if not name:
            QMessageBox.warning(self, "خطأ", "يجب إدخال اسم المنتج")
            return

        try:
            with Session(self.engine) as session:
                if not self.product:
                    self.product = Product()
                
                self.product.code = self.code_input.text().strip()
                self.product.name = name
                self.product.description = self.description_input.text().strip()
                self.product.category = self.category_input.text().strip()
                self.product.unit = self.unit_combo.currentText()
                self.product.purchase_price = self.purchase_price.value()
                self.product.sale_price = self.sale_price.value()
                self.product.quantity = self.quantity.value()
                self.product.min_quantity = self.min_quantity.value()
                self.product.barcode = self.barcode_input.text().strip()

                if not self.product.id:  # منتج جديد
                    session.add(self.product)
                session.commit()
                
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ المنتج: {str(e)}")

class InventoryWidget(QWidget):
    def __init__(self, engine):
        super().__init__()
        self.engine = engine
        self.setup_ui()
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.perform_search)
        
    def setup_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # إضافة التبويبات
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # تبويب عرض المخزون
        inventory_tab = QWidget()
        inventory_layout = QVBoxLayout()
        inventory_tab.setLayout(inventory_layout)
        
        # منطقة البحث في تبويب المخزون
        search_frame = QFrame()
        search_frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        search_layout = QHBoxLayout()
        search_frame.setLayout(search_layout)
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("بحث عن منتج...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #DEE2E6;
                border-radius: 4px;
                background-color: white;
                min-width: 300px;
            }
        """)
        self.search_input.textChanged.connect(self.on_search_changed)
        search_layout.addWidget(self.search_input)
        search_layout.addStretch()
        
        inventory_layout.addWidget(search_frame)
        
        # جدول المنتجات في تبويب المخزون
        self.products_table = QTableWidget()
        self.products_table.setStyleSheet("""
            QTableWidget {
                border: none;
                background-color: white;
            }
            QTableWidget::item {
                padding: 8px;
            }
        """)
        self.products_table.setColumnCount(7)
        self.products_table.setHorizontalHeaderLabels([
            "الكود", "اسم المنتج", "الوحدة", "سعر الشراء", 
            "سعر البيع", "الكمية", "القيمة"
        ])
        
        # تنسيق عرض الجدول
        header = self.products_table.horizontalHeader()
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # جعل عمود الاسم مرناً
        for i in [0, 2, 3, 4, 5, 6]:  # تحديد عرض ثابت لباقي الأعمدة
            header.setSectionResizeMode(i, QHeaderView.Fixed)
            header.resizeSection(i, 100)
            
        inventory_layout.addWidget(self.products_table)
        
        # تبويب إدارة الأصناف
        products_management_tab = QWidget()
        products_layout = QVBoxLayout()
        products_management_tab.setLayout(products_layout)
        
        # منطقة البحث في تبويب إدارة الأصناف
        manage_search_frame = QFrame()
        manage_search_frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        manage_search_layout = QHBoxLayout()
        manage_search_frame.setLayout(manage_search_layout)
        
        manage_search_input = QLineEdit()
        manage_search_input.setPlaceholderText("بحث عن صنف...")
        manage_search_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #DEE2E6;
                border-radius: 4px;
                background-color: white;
                min-width: 300px;
            }
        """)
        manage_search_layout.addWidget(manage_search_input)
        
        # زر إضافة صنف جديد
        add_product_btn = QPushButton("إضافة صنف جديد +")
        add_product_btn.setStyleSheet("""
            QPushButton {
                background-color: #28A745;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        add_product_btn.clicked.connect(self.show_add_product_dialog)
        manage_search_layout.addWidget(add_product_btn)
        
        products_layout.addWidget(manage_search_frame)
        
        # جدول الأصناف في تبويب الإدارة
        self.manage_products_table = QTableWidget()
        self.manage_products_table.setStyleSheet("""
            QTableWidget {
                border: none;
                background-color: white;
            }
            QTableWidget::item {
                padding: 8px;
            }
        """)
        self.manage_products_table.setColumnCount(9)
        self.manage_products_table.setHorizontalHeaderLabels([
            "الكود", "اسم الصنف", "الوحدة", "سعر الشراء", 
            "سعر البيع", "الكمية", "القيمة", "تعديل", "حذف"
        ])
        
        # تنسيق عرض جدول الإدارة
        manage_header = self.manage_products_table.horizontalHeader()
        manage_header.setSectionResizeMode(1, QHeaderView.Stretch)
        for i in [0, 2, 3, 4, 5, 6, 7, 8]:
            manage_header.setSectionResizeMode(i, QHeaderView.Fixed)
            manage_header.resizeSection(i, 100)
            
        products_layout.addWidget(self.manage_products_table)
        
        # إضافة التبويبات للـ QTabWidget
        self.tab_widget.addTab(inventory_tab, "المخزون")
        self.tab_widget.addTab(products_management_tab, "إدارة الأصناف")
        
        # تحميل المنتجات
        self.refresh_products()
        manage_search_input.textChanged.connect(lambda text: self.perform_search(text, self.manage_products_table))

    def show_add_product_dialog(self):
        dialog = ProductDialog(self.engine, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.refresh_products()
            
    def edit_product(self, product):
        dialog = ProductDialog(self.engine, product, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.refresh_products()
            
    def delete_product(self, product):
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف المنتج {product.name}؟",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                with Session(self.engine) as session:
                    product = session.merge(product)
                    session.delete(product)
                    session.commit()
                self.refresh_products()
                QMessageBox.information(self, "نجاح", "تم حذف المنتج بنجاح")
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف المنتج: {str(e)}")
    
    def on_search_changed(self):
        self.search_timer.start(300)  # تأخير البحث لتحسين الأداء
        
    def perform_search(self, search_text=None, table=None):
        if search_text is None:
            search_text = self.search_input.text().strip()
        with Session(self.engine) as session:
            query = session.query(Product)
            if search_text:
                query = query.filter(
                    or_(
                        Product.name.ilike(f"%{search_text}%"),
                        Product.code.ilike(f"%{search_text}%"),
                        Product.category.ilike(f"%{search_text}%")
                    )
                )
            products = query.all()
            self.update_table(products, table or self.products_table)
            
    def refresh_products(self):
        with Session(self.engine) as session:
            products = session.query(Product).all()
            self.update_table(products, self.products_table)
            self.update_table(products, self.manage_products_table, include_actions=True)
            
    def update_table(self, products, table, include_actions=False):
        table.setRowCount(len(products))
        
        for row, product in enumerate(products):
            # تعيين لون الخلفية للصف
            bg_color = QColor("#F8F9FA") if row % 2 == 0 else QColor("white")
            
            # إضافة بيانات المنتج
            table.setItem(row, 0, self.create_table_item(product.code or "", bg_color))
            table.setItem(row, 1, self.create_table_item(product.name, bg_color))
            table.setItem(row, 2, self.create_table_item(product.unit or "", bg_color))
            table.setItem(row, 3, self.create_table_item(f"{product.purchase_price:,.2f}", bg_color))
            table.setItem(row, 4, self.create_table_item(f"{product.sale_price:,.2f}", bg_color))
            table.setItem(row, 5, self.create_table_item(str(product.quantity), bg_color))
            
            # حساب وإضافة القيمة الإجمالية
            total_value = product.quantity * product.purchase_price
            value_item = self.create_table_item(f"{total_value:,.2f}", bg_color)
            value_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
            table.setItem(row, 6, value_item)
            
            if include_actions:
                # زر التعديل
                edit_btn = QPushButton("تعديل")
                edit_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #FFC107;
                        border: none;
                        padding: 5px 10px;
                        color: black;
                    }
                    QPushButton:hover {
                        background-color: #E0A800;
                    }
                """)
                edit_btn.clicked.connect(lambda checked, p=product: self.edit_product(p))
                table.setCellWidget(row, 7, edit_btn)
                
                # زر الحذف
                delete_btn = QPushButton("حذف")
                delete_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #DC3545;
                        border: none;
                        padding: 5px 10px;
                        color: white;
                    }
                    QPushButton:hover {
                        background-color: #C82333;
                    }
                """)
                delete_btn.clicked.connect(lambda checked, p=product: self.delete_product(p))
                table.setCellWidget(row, 8, delete_btn)
            
    def create_table_item(self, text, bg_color):
        item = QTableWidgetItem(text)
        item.setBackground(bg_color)
        return item