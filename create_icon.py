from PIL import Image, ImageDraw
import os

# إنشاء صورة جديدة بخلفية شفافة
img = Image.new('RGBA', (256, 256), color=(0, 0, 0, 0))
draw = ImageDraw.Draw(img)

# رسم دائرة زرقاء كخلفية
draw.ellipse((20, 20, 236, 236), fill=(13, 110, 253, 255))

# رسم رمز $ باللون الأبيض
draw.text((100, 80), "$", fill=(255, 255, 255, 255))

# حفظ الصورة كملف PNG
icon_path = os.path.join('img', 'paper-pin_5382614.png')
img.save(icon_path)

print(f"تم إنشاء الأيقونة بنجاح في: {icon_path}")

print(f"تم إنشاء الأيقونة بنجاح في: {icon_path}")