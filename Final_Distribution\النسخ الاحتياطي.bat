@echo off
echo أداة النسخ الاحتياطي واستعادة البيانات
echo ===================================
echo.

if not exist "backups" (
    mkdir backups
)

echo [1] إنشاء نسخة احتياطية
echo [2] استعادة نسخة احتياطية
echo [3] الخروج
echo.

set /p choice=اختر رقم العملية: 

if "%choice%"=="1" (
    echo جاري إنشاء نسخة احتياطية...
    
    set timestamp=%date:~6,4%-%date:~3,2%-%date:~0,2%_%time:~0,2%-%time:~3,2%-%time:~6,2%
    set timestamp=%timestamp: =0%
    
    if exist "accounting.db" (
        copy "accounting.db" "backups\accounting_%timestamp%.db"
        echo تم إنشاء نسخة احتياطية بنجاح: backups\accounting_%timestamp%.db
    ) else (
        echo خطأ: قاعدة البيانات غير موجودة!
    )
    
    pause
    goto start
)

if "%choice%"=="2" (
    echo النسخ الاحتياطية المتوفرة:
    echo.
    
    set count=0
    for %%f in (backups\*.db) do (
        set /a count+=1
        echo [!count!] %%~nxf
    )
    
    if %count%==0 (
        echo لا توجد نسخ احتياطية متوفرة.
        pause
        goto start
    )
    
    echo.
    set /p backup=اختر رقم النسخة الاحتياطية للاستعادة: 
    
    set count=0
    for %%f in (backups\*.db) do (
        set /a count+=1
        if !count!==%backup% (
            echo جاري استعادة النسخة الاحتياطية: %%~nxf
            
            if exist "accounting.db" (
                echo تحذير: سيتم استبدال قاعدة البيانات الحالية!
                set /p confirm=هل أنت متأكد من رغبتك في المتابعة؟ (y/n): 
                
                if "!confirm!"=="y" (
                    copy "%%f" "accounting.db"
                    echo تم استعادة النسخة الاحتياطية بنجاح.
                ) else (
                    echo تم إلغاء عملية الاستعادة.
                )
            ) else (
                copy "%%f" "accounting.db"
                echo تم استعادة النسخة الاحتياطية بنجاح.
            )
        )
    )
    
    pause
    goto start
)

if "%choice%"=="3" (
    exit
) else (
    echo اختيار غير صحيح!
    pause
    goto start
)

:start
cls
echo أداة النسخ الاحتياطي واستعادة البيانات
echo ===================================
echo.
echo [1] إنشاء نسخة احتياطية
echo [2] استعادة نسخة احتياطية
echo [3] الخروج
echo.
set /p choice=اختر رقم العملية: 
goto choice@echo off
echo أداة النسخ الاحتياطي واستعادة البيانات
echo ===================================
echo.

if not exist "backups" (
    mkdir backups
)

echo [1] إنشاء نسخة احتياطية
echo [2] استعادة نسخة احتياطية
echo [3] الخروج
echo.

set /p choice=اختر رقم العملية: 

if "%choice%"=="1" (
    echo جاري إنشاء نسخة احتياطية...
    
    set timestamp=%date:~6,4%-%date:~3,2%-%date:~0,2%_%time:~0,2%-%time:~3,2%-%time:~6,2%
    set timestamp=%timestamp: =0%
    
    if exist "accounting.db" (
        copy "accounting.db" "backups\accounting_%timestamp%.db"
        echo تم إنشاء نسخة احتياطية بنجاح: backups\accounting_%timestamp%.db
    ) else (
        echo خطأ: قاعدة البيانات غير موجودة!
    )
    
    pause
    goto start
)

if "%choice%"=="2" (
    echo النسخ الاحتياطية المتوفرة:
    echo.
    
    set count=0
    for %%f in (backups\*.db) do (
        set /a count+=1
        echo [!count!] %%~nxf
    )
    
    if %count%==0 (
        echo لا توجد نسخ احتياطية متوفرة.
        pause
        goto start
    )
    
    echo.
    set /p backup=اختر رقم النسخة الاحتياطية للاستعادة: 
    
    set count=0
    for %%f in (backups\*.db) do (
        set /a count+=1
        if !count!==%backup% (
            echo جاري استعادة النسخة الاحتياطية: %%~nxf
            
            if exist "accounting.db" (
                echo تحذير: سيتم استبدال قاعدة البيانات الحالية!
                set /p confirm=هل أنت متأكد من رغبتك في المتابعة؟ (y/n): 
                
                if "!confirm!"=="y" (
                    copy "%%f" "accounting.db"
                    echo تم استعادة النسخة الاحتياطية بنجاح.
                ) else (
                    echo تم إلغاء عملية الاستعادة.
                )
            ) else (
                copy "%%f" "accounting.db"
                echo تم استعادة النسخة الاحتياطية بنجاح.
            )
        )
    )
    
    pause
    goto start
)

if "%choice%"=="3" (
    exit
) else (
    echo اختيار غير صحيح!
    pause
    goto start
)

:start
cls
echo أداة النسخ الاحتياطي واستعادة البيانات
echo ===================================
echo.
echo [1] إنشاء نسخة احتياطية
echo [2] استعادة نسخة احتياطية
echo [3] الخروج
echo.
set /p choice=اختر رقم العملية: 
goto choice