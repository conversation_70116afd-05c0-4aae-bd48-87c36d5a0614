import os
import sys
import subprocess
import shutil

def create_executable():
    print("بدء إنشاء الملف التنفيذي...")
    
    # المسار الحالي
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # مسار الأيقونة
    icon_path = os.path.join(current_dir, 'img', 'paper-pin_5382614.png')
    
    # مسار المخرجات
    output_dir = os.path.join(current_dir, 'Final_Distribution')
    
    # إنشاء مجلد المخرجات إذا لم يكن موجودًا
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # تحديد اسم الملف التنفيذي
    exe_name = "نظام_المحاسبة_محمد_الأشرفي"
    
    # بناء أمر PyInstaller
    command = [
        'pyinstaller',
        '--name', exe_name,
        '--onefile',
        '--noconsole',
        f'--icon={icon_path}',
        '--clean',
        '--noconfirm',
        f'--distpath={output_dir}',
        '--add-data', f'gui/style.qss{os.pathsep}gui',
        '--hidden-import', 'sqlalchemy.sql.default_comparator',
        '--hidden-import', 'PyQt5.sip',
        '--hidden-import', 'reportlab.rl_config',
        '--hidden-import', 'arabic_reshaper',
        '--hidden-import', 'bidi.algorithm',
        'main.py'
    ]
    
    # تنفيذ الأمر
    try:
        subprocess.run(command, check=True)
        print(f"تم إنشاء الملف التنفيذي بنجاح في: {output_dir}")
        
        # نسخ الملفات الإضافية إلى مجلد المخرجات
        copy_additional_files(output_dir)
        
    except subprocess.CalledProcessError as e:
        print(f"حدث خطأ أثناء إنشاء الملف التنفيذي: {e}")
        return False
    
    return True

def copy_additional_files(output_dir):
    """نسخ الملفات الإضافية المطلوبة إلى مجلد المخرجات"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # إنشاء المجلدات المطلوبة
    os.makedirs(os.path.join(output_dir, 'fonts'), exist_ok=True)
    os.makedirs(os.path.join(output_dir, 'images'), exist_ok=True)
    
    # نسخ ملفات الخطوط
    fonts_dir = os.path.join(current_dir, 'fonts')
    if os.path.exists(fonts_dir):
        for file in os.listdir(fonts_dir):
            src = os.path.join(fonts_dir, file)
            dst = os.path.join(output_dir, 'fonts', file)
            if os.path.isfile(src):
                shutil.copy2(src, dst)
    
    # نسخ ملفات الصور
    images_dir = os.path.join(current_dir, 'images')
    if os.path.exists(images_dir):
        for file in os.listdir(images_dir):
            src = os.path.join(images_dir, file)
            dst = os.path.join(output_dir, 'images', file)
            if os.path.isfile(src):
                shutil.copy2(src, dst)
    
    print("تم نسخ الملفات الإضافية بنجاح")

if __name__ == "__main__":
    create_executable()