@echo off
chcp 65001 >nul
echo أداة تشخيص مشاكل نظام المحاسبة المتكامل
echo ===================================
echo.

echo جاري فحص النظام...
echo.

REM التحقق من وجود الملف التنفيذي
echo [1] فحص الملف التنفيذي:
if exist "نظام_المحاسبة_محمد_الأشرفي.exe" (
    echo ✓ الملف التنفيذي موجود
    
    REM فحص حجم الملف
    for %%A in ("نظام_المحاسبة_محمد_الأشرفي.exe") do (
        echo   - حجم الملف: %%~zA بايت
        if %%~zA LSS 1000 (
            echo   ⚠ تحذير: حجم الملف صغير جداً، قد يكون تالفاً
        )
    )
    
    REM فحص صلاحيات الملف
    echo   - فحص صلاحيات التشغيل...
    icacls "نظام_المحاسبة_محمد_الأشرفي.exe" | findstr /i "Everyone" >nul
    if %ERRORLEVEL% EQU 0 (
        echo   ✓ صلاحيات التشغيل متاحة
    ) else (
        echo   ⚠ قد تكون هناك مشكلة في صلاحيات التشغيل
    )
) else (
    echo ✗ الملف التنفيذي غير موجود!
    echo   يرجى التأكد من وجود الملف في نفس مجلد هذا الملف
)
echo.

REM التحقق من Python
echo [2] فحص Python:
python --version >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ Python مثبت
    for /f "tokens=2" %%i in ('python --version 2^>^&1') do echo   - الإصدار: %%i
    
    REM فحص pip
    pip --version >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo ✓ pip متاح
    ) else (
        echo ⚠ pip غير متاح
    )
) else (
    echo ✗ Python غير مثبت
    echo   يرجى تثبيت Python من https://python.org
)
echo.

REM التحقق من المكتبات المطلوبة
echo [3] فحص المكتبات المطلوبة:
set libraries=PyQt5 SQLAlchemy pandas reportlab arabic-reshaper python-bidi xlsxwriter matplotlib openpyxl Pillow

for %%L in (%libraries%) do (
    python -c "import %%L" >nul 2>&1
    if !ERRORLEVEL! EQU 0 (
        echo ✓ %%L مثبت
    ) else (
        echo ✗ %%L غير مثبت
    )
)
echo.

REM التحقق من الملفات والمجلدات
echo [4] فحص الملفات والمجلدات:
if exist "config.ini" (
    echo ✓ ملف الإعدادات موجود
) else (
    echo ⚠ ملف الإعدادات غير موجود
)

if exist "fonts" (
    echo ✓ مجلد الخطوط موجود
) else (
    echo ⚠ مجلد الخطوط غير موجود
)

if exist "images" (
    echo ✓ مجلد الصور موجود
) else (
    echo ⚠ مجلد الصور غير موجود
)

if exist "backups" (
    echo ✓ مجلد النسخ الاحتياطية موجود
) else (
    echo ⚠ مجلد النسخ الاحتياطية غير موجود
)
echo.

REM فحص مكافح الفيروسات
echo [5] فحص مكافح الفيروسات:
echo   قد يكون مكافح الفيروسات يمنع تشغيل الملف
echo   جرب إضافة المجلد إلى قائمة الاستثناءات
echo.

REM محاولة تشغيل الملف مع عرض الأخطاء
echo [6] محاولة تشغيل الملف مع عرض الأخطاء:
echo   جاري محاولة تشغيل الملف...
echo.

start /wait "" "نظام_المحاسبة_محمد_الأشرفي.exe" 2>&1
set exit_code=%ERRORLEVEL%

echo.
echo   كود الخروج: %exit_code%
if %exit_code% EQU 0 (
    echo   ✓ تم تشغيل الملف بنجاح
) else (
    echo   ✗ فشل في تشغيل الملف
    echo.
    echo   الأسباب المحتملة:
    echo   - مكتبات Python مفقودة
    echo   - مكافح الفيروسات يمنع التشغيل
    echo   - الملف تالف أو غير مكتمل
    echo   - مشكلة في صلاحيات النظام
    echo.
    echo   الحلول المقترحة:
    echo   1. تشغيل ملف "تثبيت المكتبات.bat"
    echo   2. إضافة المجلد لاستثناءات مكافح الفيروسات
    echo   3. تشغيل الملف كمدير (Run as Administrator)
    echo   4. التحقق من سلامة الملف
)

echo.
echo ===================================
echo انتهى التشخيص
echo ===================================
pause
