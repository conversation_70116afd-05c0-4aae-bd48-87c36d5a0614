@echo off
echo أداة إصلاح مشاكل نظام المحاسبة المتكامل
echo ===================================
echo.

echo [1] التحقق من وجود الملفات المطلوبة
echo [2] إعادة تعيين قاعدة البيانات
echo [3] إصلاح مشاكل العرض
echo [4] الخروج
echo.

set /p choice=اختر رقم العملية: 

if "%choice%"=="1" (
    echo جاري التحقق من وجود الملفات المطلوبة...
    if not exist "نظام_المحاسبة_محمد_الأشرفي.exe" (
        echo خطأ: الملف التنفيذي غير موجود!
    ) else (
        echo الملف التنفيذي موجود.
    )
    
    if not exist "fonts" (
        echo إنشاء مجلد الخطوط...
        mkdir fonts
    ) else (
        echo مجلد الخطوط موجود.
    )
    
    if not exist "images" (
        echo إنشاء مجلد الصور...
        mkdir images
    ) else (
        echo مجلد الصور موجود.
    )
    
    if not exist "config.ini" (
        echo إنشاء ملف الإعدادات...
        echo [General]> config.ini
        echo AppName=نظام المحاسبة المتكامل - محمد الأشرفي>> config.ini
        echo Version=1.0.0>> config.ini
        echo Language=ar>> config.ini
        echo.>> config.ini
        echo [Database]>> config.ini
        echo Type=SQLite>> config.ini
        echo Path=accounting.db>> config.ini
    ) else (
        echo ملف الإعدادات موجود.
    )
    
    echo تم الانتهاء من التحقق.
    pause
    goto start
)

if "%choice%"=="2" (
    echo تحذير: سيؤدي هذا الإجراء إلى حذف جميع البيانات الموجودة!
    set /p confirm=هل أنت متأكد من رغبتك في المتابعة؟ (y/n): 
    
    if "%confirm%"=="y" (
        if exist "accounting.db" (
            del /f accounting.db
            echo تم حذف قاعدة البيانات. سيتم إنشاء قاعدة بيانات جديدة عند تشغيل البرنامج.
        ) else (
            echo قاعدة البيانات غير موجودة.
        )
    ) else (
        echo تم إلغاء العملية.
    )
    
    pause
    goto start
)

if "%choice%"=="3" (
    echo جاري إصلاح مشاكل العرض...
    
    if exist "config.ini" (
        echo تحديث إعدادات العرض...
        echo [UI]> temp.ini
        echo Theme=Default>> temp.ini
        echo RTL=True>> temp.ini
        echo FontSize=12>> temp.ini
        
        type config.ini | findstr /v "[UI]" | findstr /v "Theme=" | findstr /v "RTL=" | findstr /v "FontSize=" > temp2.ini
        type temp.ini >> temp2.ini
        move /y temp2.ini config.ini
        del temp.ini
        
        echo تم تحديث إعدادات العرض.
    ) else (
        echo ملف الإعدادات غير موجود. جاري إنشاءه...
        echo [General]> config.ini
        echo AppName=نظام المحاسبة المتكامل - محمد الأشرفي>> config.ini
        echo Version=1.0.0>> config.ini
        echo Language=ar>> config.ini
        echo.>> config.ini
        echo [UI]>> config.ini
        echo Theme=Default>> config.ini
        echo RTL=True>> config.ini
        echo FontSize=12>> config.ini
    )
    
    pause
    goto start
)

if "%choice%"=="4" (
    exit
) else (
    echo اختيار غير صحيح!
    pause
    goto start
)

:start
cls
echo أداة إصلاح مشاكل نظام المحاسبة المتكامل
echo ===================================
echo.
echo [1] التحقق من وجود الملفات المطلوبة
echo [2] إعادة تعيين قاعدة البيانات
echo [3] إصلاح مشاكل العرض
echo [4] الخروج
echo.
set /p choice=اختر رقم العملية: 
goto choice@echo off
echo أداة إصلاح مشاكل نظام المحاسبة المتكامل
echo ===================================
echo.

echo [1] التحقق من وجود الملفات المطلوبة
echo [2] إعادة تعيين قاعدة البيانات
echo [3] إصلاح مشاكل العرض
echo [4] الخروج
echo.

set /p choice=اختر رقم العملية: 

if "%choice%"=="1" (
    echo جاري التحقق من وجود الملفات المطلوبة...
    if not exist "نظام_المحاسبة_محمد_الأشرفي.exe" (
        echo خطأ: الملف التنفيذي غير موجود!
    ) else (
        echo الملف التنفيذي موجود.
    )
    
    if not exist "fonts" (
        echo إنشاء مجلد الخطوط...
        mkdir fonts
    ) else (
        echo مجلد الخطوط موجود.
    )
    
    if not exist "images" (
        echo إنشاء مجلد الصور...
        mkdir images
    ) else (
        echo مجلد الصور موجود.
    )
    
    if not exist "config.ini" (
        echo إنشاء ملف الإعدادات...
        echo [General]> config.ini
        echo AppName=نظام المحاسبة المتكامل - محمد الأشرفي>> config.ini
        echo Version=1.0.0>> config.ini
        echo Language=ar>> config.ini
        echo.>> config.ini
        echo [Database]>> config.ini
        echo Type=SQLite>> config.ini
        echo Path=accounting.db>> config.ini
    ) else (
        echo ملف الإعدادات موجود.
    )
    
    echo تم الانتهاء من التحقق.
    pause
    goto start
)

if "%choice%"=="2" (
    echo تحذير: سيؤدي هذا الإجراء إلى حذف جميع البيانات الموجودة!
    set /p confirm=هل أنت متأكد من رغبتك في المتابعة؟ (y/n): 
    
    if "%confirm%"=="y" (
        if exist "accounting.db" (
            del /f accounting.db
            echo تم حذف قاعدة البيانات. سيتم إنشاء قاعدة بيانات جديدة عند تشغيل البرنامج.
        ) else (
            echo قاعدة البيانات غير موجودة.
        )
    ) else (
        echo تم إلغاء العملية.
    )
    
    pause
    goto start
)

if "%choice%"=="3" (
    echo جاري إصلاح مشاكل العرض...
    
    if exist "config.ini" (
        echo تحديث إعدادات العرض...
        echo [UI]> temp.ini
        echo Theme=Default>> temp.ini
        echo RTL=True>> temp.ini
        echo FontSize=12>> temp.ini
        
        type config.ini | findstr /v "[UI]" | findstr /v "Theme=" | findstr /v "RTL=" | findstr /v "FontSize=" > temp2.ini
        type temp.ini >> temp2.ini
        move /y temp2.ini config.ini
        del temp.ini
        
        echo تم تحديث إعدادات العرض.
    ) else (
        echo ملف الإعدادات غير موجود. جاري إنشاءه...
        echo [General]> config.ini
        echo AppName=نظام المحاسبة المتكامل - محمد الأشرفي>> config.ini
        echo Version=1.0.0>> config.ini
        echo Language=ar>> config.ini
        echo.>> config.ini
        echo [UI]>> config.ini
        echo Theme=Default>> config.ini
        echo RTL=True>> config.ini
        echo FontSize=12>> config.ini
    )
    
    pause
    goto start
)

if "%choice%"=="4" (
    exit
) else (
    echo اختيار غير صحيح!
    pause
    goto start
)

:start
cls
echo أداة إصلاح مشاكل نظام المحاسبة المتكامل
echo ===================================
echo.
echo [1] التحقق من وجود الملفات المطلوبة
echo [2] إعادة تعيين قاعدة البيانات
echo [3] إصلاح مشاكل العرض
echo [4] الخروج
echo.
set /p choice=اختر رقم العملية: 
goto choice