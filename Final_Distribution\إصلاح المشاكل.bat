@echo off
chcp 65001 >nul

:start
cls
echo أداة إصلاح مشاكل نظام المحاسبة المتكامل
echo ===================================
echo.
echo [1] التحقق من وجود الملفات المطلوبة
echo [2] إعادة تعيين قاعدة البيانات
echo [3] إصلاح مشاكل العرض
echo [4] تثبيت المكتبات المطلوبة
echo [5] الخروج
echo.

set /p choice=اختر رقم العملية:

if "%choice%"=="1" (
    echo جاري التحقق من وجود الملفات المطلوبة...
    if not exist "نظام_المحاسبة_محمد_الأشرفي.exe" (
        echo خطأ: الملف التنفيذي غير موجود!
    ) else (
        echo ✓ الملف التنفيذي موجود.
    )

    if not exist "fonts" (
        echo إنشاء مجلد الخطوط...
        mkdir fonts
        echo ✓ تم إنشاء مجلد الخطوط.
    ) else (
        echo ✓ مجلد الخطوط موجود.
    )

    if not exist "images" (
        echo إنشاء مجلد الصور...
        mkdir images
        echo ✓ تم إنشاء مجلد الصور.
    ) else (
        echo ✓ مجلد الصور موجود.
    )

    if not exist "config.ini" (
        echo إنشاء ملف الإعدادات...
        echo [General]> config.ini
        echo AppName=نظام المحاسبة المتكامل - محمد الأشرفي>> config.ini
        echo Version=1.0.0>> config.ini
        echo Language=ar>> config.ini
        echo.>> config.ini
        echo [Database]>> config.ini
        echo Type=SQLite>> config.ini
        echo Path=accounting.db>> config.ini
        echo ✓ تم إنشاء ملف الإعدادات.
    ) else (
        echo ✓ ملف الإعدادات موجود.
    )

    echo.
    echo تم الانتهاء من التحقق.
    pause
    goto start
)

if "%choice%"=="2" (
    echo تحذير: سيؤدي هذا الإجراء إلى حذف جميع البيانات الموجودة!
    set /p confirm=هل أنت متأكد من رغبتك في المتابعة؟ (y/n):

    if /i "%confirm%"=="y" (
        if exist "accounting.db" (
            del /f accounting.db
            echo ✓ تم حذف قاعدة البيانات. سيتم إنشاء قاعدة بيانات جديدة عند تشغيل البرنامج.
        ) else (
            echo قاعدة البيانات غير موجودة.
        )
    ) else (
        echo تم إلغاء العملية.
    )

    pause
    goto start
)

if "%choice%"=="3" (
    echo جاري إصلاح مشاكل العرض...

    if exist "config.ini" (
        echo تحديث إعدادات العرض...
        echo [UI]> temp.ini
        echo Theme=Default>> temp.ini
        echo RTL=True>> temp.ini
        echo FontSize=12>> temp.ini

        type config.ini | findstr /v "[UI]" | findstr /v "Theme=" | findstr /v "RTL=" | findstr /v "FontSize=" > temp2.ini 2>nul
        type temp.ini >> temp2.ini
        move /y temp2.ini config.ini >nul
        del temp.ini >nul

        echo ✓ تم تحديث إعدادات العرض.
    ) else (
        echo ملف الإعدادات غير موجود. جاري إنشاءه...
        echo [General]> config.ini
        echo AppName=نظام المحاسبة المتكامل - محمد الأشرفي>> config.ini
        echo Version=1.0.0>> config.ini
        echo Language=ar>> config.ini
        echo.>> config.ini
        echo [UI]>> config.ini
        echo Theme=Default>> config.ini
        echo RTL=True>> config.ini
        echo FontSize=12>> config.ini
        echo ✓ تم إنشاء ملف الإعدادات مع إعدادات العرض.
    )

    pause
    goto start
)

if "%choice%"=="4" (
    echo جاري تثبيت المكتبات المطلوبة...
    echo هذا قد يستغرق بضع دقائق...

    REM التحقق من وجود Python
    python --version >nul 2>&1
    if %ERRORLEVEL% NEQ 0 (
        echo خطأ: Python غير مثبت على النظام!
        echo يرجى تثبيت Python أولاً من https://python.org
        pause
        goto start
    )

    REM تثبيت المكتبات
    echo تثبيت المكتبات الأساسية...
    pip install --upgrade pip >nul 2>&1
    pip install tkinter >nul 2>&1
    pip install sqlite3 >nul 2>&1
    pip install datetime >nul 2>&1
    pip install os >nul 2>&1

    echo ✓ تم تثبيت المكتبات بنجاح.
    pause
    goto start
)

if "%choice%"=="5" (
    exit /b 0
)

echo اختيار غير صحيح!
pause
goto start
