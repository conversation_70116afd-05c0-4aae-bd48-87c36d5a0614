@echo off
echo تثبيت المكتبات المطلوبة لنظام المحاسبة المتكامل
echo ===================================
echo.

echo هذا الملف سيقوم بتثبيت المكتبات المطلوبة لتشغيل النظام.
echo يجب أن يكون لديك Python مثبت على جهازك.
echo.

set /p choice=هل تريد المتابعة؟ (y/n): 

if not "%choice%"=="y" (
    echo تم إلغاء العملية.
    pause
    exit /b
)

echo جاري التحقق من وجود Python...
python --version > nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Python غير مثبت على جهازك!
    echo يرجى تثبيت Python من الموقع الرسمي: https://www.python.org/downloads/
    pause
    exit /b
)

echo Python موجود. جاري تثبيت المكتبات...
echo.

echo تثبيت PyQt5...
python -m pip install PyQt5
echo.

echo تثبيت SQLAlchemy...
python -m pip install SQLAlchemy
echo.

echo تثبيت pandas...
python -m pip install pandas
echo.

echo تثبيت reportlab...
python -m pip install reportlab
echo.

echo تثبيت arabic-reshaper...
python -m pip install arabic-reshaper
echo.

echo تثبيت python-bidi...
python -m pip install python-bidi
echo.

echo تثبيت xlsxwriter...
python -m pip install xlsxwriter
echo.

echo تثبيت matplotlib...
python -m pip install matplotlib
echo.

echo تثبيت openpyxl...
python -m pip install openpyxl
echo.

echo تثبيت Pillow...
python -m pip install Pillow
echo.

echo تم تثبيت جميع المكتبات المطلوبة بنجاح!
echo يمكنك الآن تشغيل النظام.
echo.

pause@echo off
echo تثبيت المكتبات المطلوبة لنظام المحاسبة المتكامل
echo ===================================
echo.

echo هذا الملف سيقوم بتثبيت المكتبات المطلوبة لتشغيل النظام.
echo يجب أن يكون لديك Python مثبت على جهازك.
echo.

set /p choice=هل تريد المتابعة؟ (y/n): 

if not "%choice%"=="y" (
    echo تم إلغاء العملية.
    pause
    exit /b
)

echo جاري التحقق من وجود Python...
python --version > nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Python غير مثبت على جهازك!
    echo يرجى تثبيت Python من الموقع الرسمي: https://www.python.org/downloads/
    pause
    exit /b
)

echo Python موجود. جاري تثبيت المكتبات...
echo.

echo تثبيت PyQt5...
python -m pip install PyQt5
echo.

echo تثبيت SQLAlchemy...
python -m pip install SQLAlchemy
echo.

echo تثبيت pandas...
python -m pip install pandas
echo.

echo تثبيت reportlab...
python -m pip install reportlab
echo.

echo تثبيت arabic-reshaper...
python -m pip install arabic-reshaper
echo.

echo تثبيت python-bidi...
python -m pip install python-bidi
echo.

echo تثبيت xlsxwriter...
python -m pip install xlsxwriter
echo.

echo تثبيت matplotlib...
python -m pip install matplotlib
echo.

echo تثبيت openpyxl...
python -m pip install openpyxl
echo.

echo تثبيت Pillow...
python -m pip install Pillow
echo.

echo تم تثبيت جميع المكتبات المطلوبة بنجاح!
echo يمكنك الآن تشغيل النظام.
echo.

pause