@echo off
chcp 65001 >nul
echo تثبيت المكتبات المطلوبة لنظام المحاسبة المتكامل
echo ===================================
echo.

echo هذا الملف سيقوم بتثبيت المكتبات المطلوبة لتشغيل النظام.
echo يجب أن يكون لديك Python مثبت على جهازك.
echo.

set /p choice=هل تريد المتابعة؟ (y/n):

if /i not "%choice%"=="y" (
    echo تم إلغاء العملية.
    pause
    exit /b 0
)

echo جاري التحقق من وجود Python...
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo خطأ: Python غير مثبت على جهازك!
    echo يرجى تثبيت Python من الموقع الرسمي: https://www.python.org/downloads/
    echo.
    echo بعد تثبيت Python، أعد تشغيل هذا الملف.
    pause
    exit /b 1
)

echo ✓ Python موجود. جاري تثبيت المكتبات...
echo.

echo [1/10] تثبيت PyQt5...
python -m pip install --upgrade PyQt5 --quiet
if %ERRORLEVEL% EQU 0 (echo ✓ تم تثبيت PyQt5 بنجاح) else (echo ✗ فشل في تثبيت PyQt5)
echo.

echo [2/10] تثبيت SQLAlchemy...
python -m pip install --upgrade SQLAlchemy --quiet
if %ERRORLEVEL% EQU 0 (echo ✓ تم تثبيت SQLAlchemy بنجاح) else (echo ✗ فشل في تثبيت SQLAlchemy)
echo.

echo [3/10] تثبيت pandas...
python -m pip install --upgrade pandas --quiet
if %ERRORLEVEL% EQU 0 (echo ✓ تم تثبيت pandas بنجاح) else (echo ✗ فشل في تثبيت pandas)
echo.

echo [4/10] تثبيت reportlab...
python -m pip install --upgrade reportlab --quiet
if %ERRORLEVEL% EQU 0 (echo ✓ تم تثبيت reportlab بنجاح) else (echo ✗ فشل في تثبيت reportlab)
echo.

echo [5/10] تثبيت arabic-reshaper...
python -m pip install --upgrade arabic-reshaper --quiet
if %ERRORLEVEL% EQU 0 (echo ✓ تم تثبيت arabic-reshaper بنجاح) else (echo ✗ فشل في تثبيت arabic-reshaper)
echo.

echo [6/10] تثبيت python-bidi...
python -m pip install --upgrade python-bidi --quiet
if %ERRORLEVEL% EQU 0 (echo ✓ تم تثبيت python-bidi بنجاح) else (echo ✗ فشل في تثبيت python-bidi)
echo.

echo [7/10] تثبيت xlsxwriter...
python -m pip install --upgrade xlsxwriter --quiet
if %ERRORLEVEL% EQU 0 (echo ✓ تم تثبيت xlsxwriter بنجاح) else (echo ✗ فشل في تثبيت xlsxwriter)
echo.

echo [8/10] تثبيت matplotlib...
python -m pip install --upgrade matplotlib --quiet
if %ERRORLEVEL% EQU 0 (echo ✓ تم تثبيت matplotlib بنجاح) else (echo ✗ فشل في تثبيت matplotlib)
echo.

echo [9/10] تثبيت openpyxl...
python -m pip install --upgrade openpyxl --quiet
if %ERRORLEVEL% EQU 0 (echo ✓ تم تثبيت openpyxl بنجاح) else (echo ✗ فشل في تثبيت openpyxl)
echo.

echo [10/10] تثبيت Pillow...
python -m pip install --upgrade Pillow --quiet
if %ERRORLEVEL% EQU 0 (echo ✓ تم تثبيت Pillow بنجاح) else (echo ✗ فشل في تثبيت Pillow)
echo.

echo ===================================
echo ✓ تم الانتهاء من تثبيت جميع المكتبات!
echo يمكنك الآن تشغيل النظام باستخدام ملف "تشغيل النظام.bat"
echo ===================================
echo.

pause