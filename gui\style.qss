QWidget {
    font-family: Arial;
    font-size: 12px;
}

QPushButton {
    padding: 5px 10px;
    border-radius: 3px;
    background-color: #3498db;
    color: white;
    min-width: 80px;
}

QPushButton:hover {
    background-color: #2980b9;
}

QLineEdit, QComboBox, QSpinBox, QDateEdit {
    padding: 5px;
    border: 1px solid #bdc3c7;
    border-radius: 3px;
}

QTableWidget {
    border: 1px solid #bdc3c7;
    gridline-color: #ecf0f1;
}

QTableWidget::item {
    padding: 5px;
}

QHeaderView::section {
    background-color: #34495e;
    color: white;
    padding: 5px;
    border: none;
}

QTabWidget::pane {
    border: 1px solid #bdc3c7;
}

QTabBar::tab {
    background-color: #ecf0f1;
    padding: 8px 15px;
    margin-right: 2px;
}

QTabBar::tab:selected {
    background-color: #3498db;
    color: white;
}

QMenuBar {
    background-color: #34495e;
    color: white;
}

QMenuBar::item {
    padding: 5px 10px;
}

QMenuBar::item:selected {
    background-color: #2c3e50;
}

QMenu {
    background-color: white;
    border: 1px solid #bdc3c7;
}

QMenu::item {
    padding: 5px 20px;
}

QMenu::item:selected {
    background-color: #3498db;
    color: white;
}

QGroupBox {
    border: 1px solid #bdc3c7;
    border-radius: 3px;
    margin-top: 1em;
    padding-top: 10px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top center;
    padding: 0 3px;
}

QStatusBar {
    background-color: #ecf0f1;
}

QLabel {
    color: #2c3e50;
}